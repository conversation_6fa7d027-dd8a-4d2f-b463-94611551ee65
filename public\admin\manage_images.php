<?php
session_start();
require_once __DIR__ . '/../../config/db_connect.php';
require_once __DIR__ . '/../../config/functions.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_hero_background'])) {
        $newBackgroundImage = trim($_POST['hero_background_image']);
        
        if (!empty($newBackgroundImage)) {
            updateSetting($pdo, 'hero_background_image', $newBackgroundImage);
            $success = "تم تحديث صورة خلفية الـ Hero بنجاح!";
        } else {
            $error = "يرجى إدخال مسار صورة صحيح";
        }
    }
    
    if (isset($_POST['update_about_image'])) {
        $newAboutImage = trim($_POST['about_section_image']);
        
        if (!empty($newAboutImage)) {
            updateSetting($pdo, 'about_section_image', $newAboutImage);
            $success = "تم تحديث صورة قسم About بنجاح!";
        } else {
            $error = "يرجى إدخال مسار صورة صحيح";
        }
    }
}

// Get current images
$currentHeroBackground = getSetting($pdo, 'hero_background_image', 'images/hero/default-hero-bg.jpg');
$currentAboutImage = getSetting($pdo, 'about_section_image', 'images/about/default-about.jpg');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة صور الموقع - الزائرين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .preview-container {
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.9) 0%, rgba(59, 130, 246, 0.8) 100%);
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        .preview-content {
            position: relative;
            z-index: 2;
        }
        .about-preview {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
            border-radius: 15px;
            min-height: 250px;
        }
        .about-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.8) 0%, rgba(59, 130, 246, 0.7) 50%, rgba(30, 64, 175, 0.8) 100%);
            z-index: 1;
            border-radius: 15px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><?= $success ?>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i><?= $error ?>
                    </div>
                <?php endif; ?>

                <!-- Hero Background Section -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-image me-2"></i>صورة خلفية الـ Hero
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Current Preview -->
                        <div class="mb-4">
                            <h5>المعاينة الحالية:</h5>
                            <div class="preview-container" id="heroPreview" style="background-image: url('<?= htmlspecialchars($currentHeroBackground) ?>');">
                                <div class="preview-content">
                                    <h3 class="fw-bold mb-2">الزائرين - حملات عمرة من الرياض</h3>
                                    <p class="mb-0">شريكك الموثوق لأفضل حملات العمرة</p>
                                </div>
                            </div>
                        </div>

                        <!-- Update Form -->
                        <form method="POST" class="mb-3">
                            <div class="mb-3">
                                <label for="hero_background_image" class="form-label">
                                    <i class="fas fa-link me-2"></i>مسار صورة خلفية الـ Hero
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="hero_background_image" 
                                       name="hero_background_image" 
                                       value="<?= htmlspecialchars($currentHeroBackground) ?>"
                                       placeholder="مثال: images/hero/my-background.jpg"
                                       required>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="submit" name="update_hero_background" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ صورة الـ Hero
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="previewHeroImage()">
                                    <i class="fas fa-eye me-2"></i>معاينة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- About Section Image -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-image me-2"></i>صورة قسم About
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Current Preview -->
                        <div class="mb-4">
                            <h5>المعاينة الحالية:</h5>
                            <div class="preview-container about-preview" id="aboutPreview" style="background-image: url('<?= htmlspecialchars($currentAboutImage) ?>');">
                                <div class="preview-content">
                                    <i class="fas fa-kaaba fa-3x text-white mb-2"></i>
                                    <h4 class="fw-bold mb-2">أفضل حملة عمرة من الرياض</h4>
                                    <p class="mb-0 text-white-50">خدمات متميزة وأسعار منافسة</p>
                                </div>
                            </div>
                        </div>

                        <!-- Update Form -->
                        <form method="POST" class="mb-3">
                            <div class="mb-3">
                                <label for="about_section_image" class="form-label">
                                    <i class="fas fa-link me-2"></i>مسار صورة قسم About
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="about_section_image" 
                                       name="about_section_image" 
                                       value="<?= htmlspecialchars($currentAboutImage) ?>"
                                       placeholder="مثال: images/about/my-about-image.jpg"
                                       required>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="submit" name="update_about_image" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>حفظ صورة About
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="previewAboutImage()">
                                    <i class="fas fa-eye me-2"></i>معاينة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card shadow">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <a href="../index.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-home me-2"></i>عرض الصفحة الرئيسية
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button class="btn btn-outline-secondary w-100" onclick="resetToDefaults()">
                                    <i class="fas fa-undo me-2"></i>استعادة الصور الافتراضية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewHeroImage() {
            const imageUrl = document.getElementById('hero_background_image').value;
            const preview = document.getElementById('heroPreview');
            
            if (imageUrl.trim()) {
                preview.style.backgroundImage = `url('${imageUrl}')`;
            }
        }
        
        function previewAboutImage() {
            const imageUrl = document.getElementById('about_section_image').value;
            const preview = document.getElementById('aboutPreview');
            
            if (imageUrl.trim()) {
                preview.style.backgroundImage = `url('${imageUrl}')`;
            }
        }
        
        function resetToDefaults() {
            if (confirm('هل أنت متأكد من استعادة الصور الافتراضية؟')) {
                document.getElementById('hero_background_image').value = 'images/hero/default-hero-bg.jpg';
                document.getElementById('about_section_image').value = 'images/about/default-about.jpg';
                previewHeroImage();
                previewAboutImage();
            }
        }
    </script>
</body>
</html>
