<?php
session_start();
require_once __DIR__ . '/../config/db_connect.php';

// Get hotels from database
try {
    $stmt = $pdo->prepare("
        SELECT h.*, c.name as city_name
        FROM hotels h
        LEFT JOIN cities c ON h.city_id = c.id
        ORDER BY h.stars DESC, h.name ASC
    ");
    $stmt->execute();
    $hotels = $stmt->fetchAll();
} catch(PDOException $e) {
    $hotels = [];
    error_log("Error fetching hotels: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فنادق العمرة المختارة - الزائرين</title>
    <meta name="description" content="الزائرين - فنادق العمرة المختارة بعناية لحملات العمرة من الرياض">
    <meta name="keywords" content="فنادق العمرة, حملات عمرة من الرياض, الزائرين, فنادق مكة">
    <link rel="canonical" href="https://alzaarin.net/hotels.php">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container py-5">
        <h1 class="mb-4 text-center">فنادق العمرة المختارة</h1>

        <!-- Hotels List -->
        <div class="row">
            <?php foreach ($hotels as $hotel): ?>
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="card h-100 hotel-card">
                    <div class="hotel-image-placeholder">
                        <div class="hotel-image-content">
                            <i class="fas fa-hotel fa-3x text-white mb-3"></i>
                            <h6 class="text-white"><?= htmlspecialchars($hotel['name']) ?></h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title text-primary"><?= htmlspecialchars($hotel['name']) ?></h5>
                        <p class="card-text text-muted small"><?= htmlspecialchars($hotel['description']) ?></p>
                        <div class="mb-2">
                            <strong>التقييم:</strong>
                            <?php for($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star <?= $i <= $hotel['stars'] ? 'text-warning' : 'text-muted' ?>"></i>
                            <?php endfor; ?>
                        </div>
                        <div class="mb-2">
                            <strong>الموقع:</strong> <?= htmlspecialchars($hotel['city_name']) ?>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <a href="hotel_details.php?id=<?= $hotel['id'] ?>" class="btn btn-primary w-100">
                            <i class="fas fa-eye me-1"></i>تفاصيل الفندق
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/script.js"></script>
</body>
</html>