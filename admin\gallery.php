<?php
session_start();
require_once '../config/db_connect.php';

// Check if user is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

// Handle image upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_images'])) {
    $category = $_POST['category'];
    $uploadDir = '../public/uploads/gallery/';
    
    // Create directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    
    $uploadedFiles = [];
    $errors = [];
    
    if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
        $fileCount = count($_FILES['images']['name']);
        
        for ($i = 0; $i < $fileCount; $i++) {
            $fileName = $_FILES['images']['name'][$i];
            $fileTmpName = $_FILES['images']['tmp_name'][$i];
            $fileSize = $_FILES['images']['size'][$i];
            $fileError = $_FILES['images']['error'][$i];
            
            if ($fileError === 0) {
                $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                $allowedExts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                
                if (in_array($fileExt, $allowedExts)) {
                    if ($fileSize < 5000000) { // 5MB limit
                        $newFileName = uniqid('gallery_', true) . '.' . $fileExt;
                        $fileDestination = $uploadDir . $newFileName;
                        
                        if (move_uploaded_file($fileTmpName, $fileDestination)) {
                            // Save to database
                            $title = $_POST['titles'][$i] ?? 'صورة جديدة';
                            $description = $_POST['descriptions'][$i] ?? '';
                            $imagePath = 'uploads/gallery/' . $newFileName;
                            
                            $stmt = $pdo->prepare("INSERT INTO gallery_images (title, description, image_path, category) VALUES (?, ?, ?, ?)");
                            $stmt->execute([$title, $description, $imagePath, $category]);
                            
                            $uploadedFiles[] = $fileName;
                        } else {
                            $errors[] = "فشل في رفع الملف: $fileName";
                        }
                    } else {
                        $errors[] = "حجم الملف كبير جداً: $fileName";
                    }
                } else {
                    $errors[] = "نوع الملف غير مدعوم: $fileName";
                }
            } else {
                $errors[] = "خطأ في رفع الملف: $fileName";
            }
        }
    }
    
    if (!empty($uploadedFiles)) {
        $success_message = "تم رفع " . count($uploadedFiles) . " صورة بنجاح";
    }
    if (!empty($errors)) {
        $error_message = implode('<br>', $errors);
    }
}

// Handle image deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $imageId = $_GET['delete'];
    
    // Get image path before deletion
    $stmt = $pdo->prepare("SELECT image_path FROM gallery_images WHERE id = ?");
    $stmt->execute([$imageId]);
    $image = $stmt->fetch();
    
    if ($image) {
        // Delete file from server
        $filePath = '../public/' . $image['image_path'];
        if (file_exists($filePath)) {
            unlink($filePath);
        }
        
        // Delete from database
        $stmt = $pdo->prepare("DELETE FROM gallery_images WHERE id = ?");
        $stmt->execute([$imageId]);
        
        $success_message = "تم حذف الصورة بنجاح";
    }
}

// Get all images
$stmt = $pdo->prepare("SELECT * FROM gallery_images ORDER BY category, sort_order, created_at DESC");
$stmt->execute();
$images = $stmt->fetchAll();

// Separate images by category
$busImages = array_filter($images, function($img) { return $img['category'] === 'buses'; });
$hotelImages = array_filter($images, function($img) { return $img['category'] === 'hotels'; });
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة معرض الصور - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../public/css/style.css">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        .image-preview {
            max-width: 100px;
            max-height: 100px;
            object-fit: cover;
            border-radius: 8px;
        }
        .image-card {
            transition: transform 0.2s ease;
        }
        .image-card:hover {
            transform: translateY(-5px);
        }
        .dynamic-inputs {
            display: none;
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-images me-2"></i>إدارة معرض الصور</h1>
                </div>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?= $success_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?= $error_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Upload Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cloud-upload-alt me-2"></i>رفع صور جديدة</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" id="uploadForm">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="category" class="form-label">فئة الصور</label>
                                    <select class="form-select" id="category" name="category" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="buses">الباصات</option>
                                        <option value="hotels">الفنادق</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h5>اسحب الصور هنا أو انقر للاختيار</h5>
                                <p class="text-muted">يمكنك اختيار صور متعددة (JPG, PNG, GIF, WEBP)</p>
                                <input type="file" id="images" name="images[]" multiple accept="image/*" class="d-none">
                            </div>
                            
                            <div id="dynamicInputs" class="dynamic-inputs mt-4">
                                <h6>تفاصيل الصور:</h6>
                                <div id="imageInputs"></div>
                            </div>
                            
                            <div class="mt-3">
                                <button type="submit" name="upload_images" class="btn btn-primary">
                                    <i class="fas fa-upload me-2"></i>رفع الصور
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Images Gallery -->
                <div class="row">
                    <!-- Buses Images -->
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-bus me-2"></i>صور الباصات (<?= count($busImages) ?>)</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($busImages)): ?>
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-images fa-3x mb-3"></i>
                                        <p>لا توجد صور للباصات حتى الآن</p>
                                    </div>
                                <?php else: ?>
                                    <div class="row g-3">
                                        <?php foreach ($busImages as $image): ?>
                                            <div class="col-lg-3 col-md-4 col-sm-6">
                                                <div class="card image-card">
                                                    <img src="../public/<?= $image['image_path'] ?>" class="card-img-top" style="height: 200px; object-fit: cover;" alt="<?= htmlspecialchars($image['title']) ?>">
                                                    <div class="card-body p-2">
                                                        <h6 class="card-title mb-1"><?= htmlspecialchars($image['title']) ?></h6>
                                                        <p class="card-text small text-muted"><?= htmlspecialchars($image['description']) ?></p>
                                                        <div class="d-flex justify-content-between">
                                                            <small class="text-muted"><?= date('Y-m-d', strtotime($image['created_at'])) ?></small>
                                                            <a href="?delete=<?= $image['id'] ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الصورة؟')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Hotels Images -->
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-hotel me-2"></i>صور الفنادق (<?= count($hotelImages) ?>)</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($hotelImages)): ?>
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-images fa-3x mb-3"></i>
                                        <p>لا توجد صور للفنادق حتى الآن</p>
                                    </div>
                                <?php else: ?>
                                    <div class="row g-3">
                                        <?php foreach ($hotelImages as $image): ?>
                                            <div class="col-lg-3 col-md-4 col-sm-6">
                                                <div class="card image-card">
                                                    <img src="../public/<?= $image['image_path'] ?>" class="card-img-top" style="height: 200px; object-fit: cover;" alt="<?= htmlspecialchars($image['title']) ?>">
                                                    <div class="card-body p-2">
                                                        <h6 class="card-title mb-1"><?= htmlspecialchars($image['title']) ?></h6>
                                                        <p class="card-text small text-muted"><?= htmlspecialchars($image['description']) ?></p>
                                                        <div class="d-flex justify-content-between">
                                                            <small class="text-muted"><?= date('Y-m-d', strtotime($image['created_at'])) ?></small>
                                                            <a href="?delete=<?= $image['id'] ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الصورة؟')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('images');
            const dynamicInputs = document.getElementById('dynamicInputs');
            const imageInputs = document.getElementById('imageInputs');

            // Click to select files
            uploadArea.addEventListener('click', function() {
                fileInput.click();
            });

            // Drag and drop functionality
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                fileInput.files = e.dataTransfer.files;
                handleFileSelection();
            });

            // File selection handler
            fileInput.addEventListener('change', handleFileSelection);

            function handleFileSelection() {
                const files = fileInput.files;
                if (files.length > 0) {
                    dynamicInputs.style.display = 'block';
                    imageInputs.innerHTML = '';

                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            const imageDiv = document.createElement('div');
                            imageDiv.className = 'row mb-3 p-3 border rounded';
                            imageDiv.innerHTML = `
                                <div class="col-md-2">
                                    <img src="${e.target.result}" class="image-preview w-100" alt="Preview">
                                </div>
                                <div class="col-md-5">
                                    <label class="form-label">عنوان الصورة</label>
                                    <input type="text" class="form-control" name="titles[]" placeholder="أدخل عنوان الصورة" required>
                                </div>
                                <div class="col-md-5">
                                    <label class="form-label">وصف الصورة</label>
                                    <input type="text" class="form-control" name="descriptions[]" placeholder="أدخل وصف الصورة">
                                </div>
                            `;
                            imageInputs.appendChild(imageDiv);
                        };

                        reader.readAsDataURL(file);
                    }
                } else {
                    dynamicInputs.style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>
