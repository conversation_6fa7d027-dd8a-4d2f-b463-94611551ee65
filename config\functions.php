<?php
// Helper functions for the Umrah website

// Function to sanitize input data
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Function to display error message
function displayError($message) {
    return '<div class="alert alert-danger">' . $message . '</div>';
}

// Function to display success message
function displaySuccess($message) {
    return '<div class="alert alert-success">' . $message . '</div>';
}

// Function to redirect with a message
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
    header("Location: $url");
    exit();
}

// Function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

// Function to require login
function requireLogin() {
    if (!isLoggedIn()) {
        redirectWithMessage('admin/login.php', 'Please log in to access this page', 'danger');
    }
}

// Function to get cities
function getCities($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM cities ORDER BY name");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

// Settings management functions
function getSetting($pdo, $key, $default = '') {
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        return $result ? $result['setting_value'] : $default;
    } catch (PDOException $e) {
        return $default;
    }
}

function updateSetting($pdo, $key, $value) {
    try {
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        return $stmt->execute([$key, $value]);
    } catch (PDOException $e) {
        return false;
    }
}

// WhatsApp helper function
function getWhatsAppURL($pdo, $message = '') {
    $whatsappNumber = getSetting($pdo, 'whatsapp_number', '966501234567');
    $encodedMessage = urlencode($message);
    return "https://wa.me/{$whatsappNumber}?text={$encodedMessage}";
}

// Function to get hotels with city info
function getHotels($pdo, $cityId = null, $stars = null) {
    try {
        $sql = "SELECT h.*, c.name as city_name 
                FROM hotels h 
                JOIN cities c ON h.city_id = c.id";
        
        $params = [];
        
        if ($cityId !== null) {
            $sql .= " WHERE h.city_id = ?";
            $params[] = $cityId;
            
            if ($stars !== null) {
                $sql .= " AND h.stars = ?";
                $params[] = $stars;
            }
        } elseif ($stars !== null) {
            $sql .= " WHERE h.stars = ?";
            $params[] = $stars;
        }
        
        $sql .= " ORDER BY h.name";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

// Function to get buses
function getBuses($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM buses ORDER BY name");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

// Function to get trips with related info
function getTrips($pdo, $fromCityId = null, $toCityId = null) {
    try {
        $sql = "SELECT t.*, 
                fc.name as from_city, 
                tc.name as to_city, 
                h.name as hotel_name, 
                h.stars as hotel_stars,
                b.name as bus_name
                FROM trips t 
                JOIN cities fc ON t.from_city_id = fc.id
                JOIN cities tc ON t.to_city_id = tc.id
                JOIN hotels h ON t.hotel_id = h.id
                JOIN buses b ON t.bus_id = b.id";
        
        $params = [];
        
        if ($fromCityId !== null) {
            $sql .= " WHERE t.from_city_id = ?";
            $params[] = $fromCityId;
            
            if ($toCityId !== null) {
                $sql .= " AND t.to_city_id = ?";
                $params[] = $toCityId;
            }
        } elseif ($toCityId !== null) {
            $sql .= " WHERE t.to_city_id = ?";
            $params[] = $toCityId;
        }
        
        $sql .= " ORDER BY t.start_date";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

// Function to get a specific trip with details
function getTripDetails($pdo, $tripId) {
    try {
        $sql = "SELECT t.*, 
                fc.name as from_city, 
                tc.name as to_city, 
                h.name as hotel_name, 
                h.stars as hotel_stars,
                h.description as hotel_description,
                h.image_url as hotel_image,
                b.name as bus_name,
                b.description as bus_description,
                b.image_url as bus_image
                FROM trips t 
                JOIN cities fc ON t.from_city_id = fc.id
                JOIN cities tc ON t.to_city_id = tc.id
                JOIN hotels h ON t.hotel_id = h.id
                JOIN buses b ON t.bus_id = b.id
                WHERE t.id = ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$tripId]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        return null;
    }
}

// Format date to a readable format in Arabic
function formatDate($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];

    $timestamp = strtotime($date);
    $day = date('j', $timestamp);
    $month = $months[(int)date('n', $timestamp)];
    $year = date('Y', $timestamp);

    return "$day $month $year";
}
?>