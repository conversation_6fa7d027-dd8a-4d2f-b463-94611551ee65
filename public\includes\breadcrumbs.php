<?php
function generateBreadcrumbs($currentPage, $customTitle = '') {
    $breadcrumbs = [
        'index.php' => 'الرئيسية',
        'trips.php' => 'حملات العمرة',
        'hotels.php' => 'الفنادق',
        'buses.php' => 'وسائل النقل',
        'contact.php' => 'اتصل بنا'
    ];
    
    $currentFile = basename($_SERVER['PHP_SELF']);
    $title = $customTitle ?: ($breadcrumbs[$currentFile] ?? 'صفحة غير معروفة');
    
    echo '<nav aria-label="breadcrumb" class="bg-light py-2">';
    echo '<div class="container">';
    echo '<ol class="breadcrumb mb-0">';
    echo '<li class="breadcrumb-item"><a href="index.php" class="text-decoration-none">الرئيسية</a></li>';
    
    if ($currentFile !== 'index.php') {
        echo '<li class="breadcrumb-item active" aria-current="page">' . htmlspecialchars($title) . '</li>';
    }
    
    echo '</ol>';
    echo '</div>';
    echo '</nav>';
    
    // Schema markup for breadcrumbs
    $schema = [
        "@context" => "https://schema.org",
        "@type" => "BreadcrumbList",
        "itemListElement" => [
            [
                "@type" => "ListItem",
                "position" => 1,
                "name" => "الرئيسية",
                "item" => "https://alzaarin.net/"
            ]
        ]
    ];
    
    if ($currentFile !== 'index.php') {
        $schema["itemListElement"][] = [
            "@type" => "ListItem",
            "position" => 2,
            "name" => $title,
            "item" => "https://alzaarin.net/" . $currentFile
        ];
    }
    
    echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_UNICODE) . '</script>';
}
?>
