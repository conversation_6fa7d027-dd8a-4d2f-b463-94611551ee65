<?php
session_start();
require_once __DIR__ . '/../config/db_connect.php';
require_once __DIR__ . '/../config/functions.php';

// Get WhatsApp number from settings
$whatsappNumber = getSetting($pdo, 'whatsapp_number', '966501234567');

// Get trip ID from URL
$tripId = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Get trip details
$trip = getTripDetails($pdo, $tripId);

// Redirect if trip not found
if (!$trip) {
    redirectWithMessage('trips.php', 'Trip not found', 'danger');
}

// Calculate trip duration
$tripDuration = 3; // Default 3 days for all trips
$availability = (strpos($trip['title'], 'VIP') !== false) ? 'الاثنين والخميس' : 'متوفرة يومياً';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $trip['title'] ?> - رحلات العمرة من الرياض</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container py-5">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="trips.php">رحلات العمرة</a></li>
                <li class="breadcrumb-item active" aria-current="page"><?= $trip['title'] ?></li>
            </ol>
        </nav>

        <div class="row mb-4">
            <div class="col-md-8">
                <h1 class="mb-3"><?= $trip['title'] ?></h1>
                <p class="lead">
                    <i class="fas fa-map-marker-alt me-2"></i>من <?= $trip['from_city'] ?> إلى <?= $trip['to_city'] ?>
                </p>
                
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><i class="fas fa-calendar-alt me-2"></i> <strong>المواعيد:</strong> <?= $availability ?></p>
                                <p><i class="fas fa-clock me-2"></i> <strong>المدة:</strong> <?= $tripDuration ?> أيام</p>
                            </div>
                            <div class="col-md-6">
                                <p><i class="fas fa-hotel me-2"></i> <strong>الفندق:</strong> <?= $trip['hotel_name'] ?></p>
                                <p>
                                    <i class="fas fa-star me-2"></i> <strong>التقييم:</strong>
                                    <?php for($i = 0; $i < $trip['hotel_stars']; $i++): ?>
                                        <i class="fas fa-star text-warning"></i>
                                    <?php endfor; ?>
                                </p>
                                <p><i class="fas fa-bus me-2"></i> <strong>النقل:</strong> <?= $trip['bus_name'] ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h3>معلومات الرحله</h3>
                <div class="card mb-4">
                    <div class="card-body">
                        <?= nl2br($trip['description']) ?>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h4><i class="fas fa-hotel me-2"></i>Hotel Details</h4>
                            </div>
                            <img src="<?= $trip['hotel_image'] ? $trip['hotel_image'] : 'images/hotels/default.jpg' ?>" 
                                 class="card-img-top" alt="<?= $trip['hotel_name'] ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?= $trip['hotel_name'] ?></h5>
                                <p class="card-text"><?= nl2br($trip['hotel_description']) ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h4><i class="fas fa-bus me-2"></i>Transportation Details</h4>
                            </div>
                            <img src="<?= $trip['bus_image'] ? $trip['bus_image'] : 'images/buses/default.jpg' ?>" 
                                 class="card-img-top" alt="<?= $trip['bus_name'] ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?= $trip['bus_name'] ?></h5>
                                <p class="card-text"><?= nl2br($trip['bus_description']) ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card booking-form sticky-top" style="top: 20px;">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">تفاصيل الرحله</h4>
                    </div>
                    <div class="card-body">
                        <div class="mb-4 text-center">
                            <h3 class="text-primary"><?= isset($trip['price']) ? number_format($trip['price'], 2) : '0.00' ?> SAR</h3>
                            <p class="text-muted">per person</p>
                        </div>
                        
                        <ul class="list-group list-group-flush mb-4">
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-calendar me-2"></i>المواعيد</span>
                                <strong><?= $availability ?></strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-clock me-2"></i>المدة</span>
                                <strong><?= $tripDuration ?> أيام</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-hotel me-2"></i>الفندق</span>
                                <strong><?= $trip['hotel_name'] ?></strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-bus me-2"></i>النقل</span>
                                <strong><?= $trip['bus_name'] ?></strong>
                            </li>
                        </ul>

                        <a href="<?= getWhatsAppURL($pdo, 'مرحباً، أريد الاستفسار عن رحلة ' . $trip['title']) ?>"
                           class="btn btn-success btn-lg w-100"
                           target="_blank">
                            <i class="fab fa-whatsapp me-2"></i>تواصل واتساب
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/script.js"></script>
</body>
</html>