// Main JavaScript file for the Umrah website

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Initialize date pickers if they exist
    if (typeof flatpickr !== 'undefined') {
        flatpickr('.date-picker', {
            dateFormat: 'Y-m-d',
            minDate: 'today'
        });
    }

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Filter functionality for hotels
    const citySelect = document.getElementById('city-filter');
    const starsSelect = document.getElementById('stars-filter');
    
    if (citySelect && starsSelect) {
        citySelect.addEventListener('change', updateFilters);
        starsSelect.addEventListener('change', updateFilters);
        
        function updateFilters() {
            const cityId = citySelect.value;
            const stars = starsSelect.value;
            
            let url = 'hotels.php';
            const params = [];
            
            if (cityId) {
                params.push('city_id=' + cityId);
            }
            
            if (stars) {
                params.push('stars=' + stars);
            }
            
            if (params.length > 0) {
                url += '?' + params.join('&');
            }
            
            window.location.href = url;
        }
    }
    
    // Trip search functionality
    const fromCitySelect = document.getElementById('from-city');
    const toCitySelect = document.getElementById('to-city');
    const searchButton = document.getElementById('search-trips');
    
    if (fromCitySelect && toCitySelect && searchButton) {
        searchButton.addEventListener('click', function() {
            const fromCity = fromCitySelect.value;
            const toCity = toCitySelect.value;
            
            let url = 'trips.php';
            const params = [];
            
            if (fromCity) {
                params.push('from=' + fromCity);
            }
            
            if (toCity) {
                params.push('to=' + toCity);
            }
            
            if (params.length > 0) {
                url += '?' + params.join('&');
            }
            
            window.location.href = url;
        });
    }
    
    // Booking form passenger count calculation
    const passengerCount = document.getElementById('num_passengers');
    const totalPrice = document.getElementById('total_price');
    const basePrice = document.getElementById('base_price');
    
    if (passengerCount && totalPrice && basePrice) {
        passengerCount.addEventListener('change', updateTotalPrice);
        passengerCount.addEventListener('keyup', updateTotalPrice);
        
        function updateTotalPrice() {
            const count = parseInt(passengerCount.value) || 1;
            const price = parseFloat(basePrice.value) || 0;
            
            totalPrice.textContent = (count * price).toFixed(2) + ' SAR';
        }
    }
});