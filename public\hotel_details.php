<?php
session_start();
require_once __DIR__ . '/../config/db_connect.php';

// Get hotel ID from URL
$hotelId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$hotelId) {
    header('Location: hotels.php');
    exit();
}

// Get hotel details
try {
    $stmt = $pdo->prepare("
        SELECT h.*, c.name as city_name 
        FROM hotels h 
        LEFT JOIN cities c ON h.city_id = c.id 
        WHERE h.id = ?
    ");
    $stmt->execute([$hotelId]);
    $hotel = $stmt->fetch();
    
    if (!$hotel) {
        header('Location: hotels.php');
        exit();
    }
} catch(PDOException $e) {
    header('Location: hotels.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($hotel['name']) ?> - فنادق العمرة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container py-5">
        <!-- Hotel Header -->
        <div class="row mb-5">
            <div class="col-lg-8">
                <div class="hotel-image-placeholder mb-4" style="height: 400px;">
                    <div class="hotel-image-content">
                        <i class="fas fa-hotel fa-5x text-white mb-3"></i>
                        <h3 class="text-white"><?= htmlspecialchars($hotel['name']) ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">معلومات الفندق</h4>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title text-primary"><?= htmlspecialchars($hotel['name']) ?></h5>
                        
                        <div class="mb-3">
                            <strong>التقييم:</strong><br>
                            <?php for($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star <?= $i <= $hotel['stars'] ? 'text-warning' : 'text-muted' ?>"></i>
                            <?php endfor; ?>
                            <span class="ms-2">(<?= $hotel['stars'] ?> نجوم)</span>
                        </div>
                        
                        <div class="mb-3">
                            <strong>الموقع:</strong><br>
                            <i class="fas fa-map-marker-alt text-primary me-1"></i>
                            <?= htmlspecialchars($hotel['city_name']) ?>
                        </div>
                        

                        
                        <div class="d-grid gap-2">
                            <a href="trips.php" class="btn btn-primary">
                                <i class="fas fa-route me-1"></i>البحث عن رحلات
                            </a>
                            <a href="contact.php" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-1"></i>اتصل بنا للحجز
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hotel Description -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">وصف الفندق</h4>
                    </div>
                    <div class="card-body">
                        <p class="lead"><?= htmlspecialchars($hotel['description']) ?></p>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h5>المميزات:</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-wifi text-primary me-2"></i>إنترنت مجاني</li>
                                    <li><i class="fas fa-car text-primary me-2"></i>موقف سيارات</li>
                                    <li><i class="fas fa-utensils text-primary me-2"></i>مطعم</li>
                                    <li><i class="fas fa-concierge-bell text-primary me-2"></i>خدمة الغرف</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5>الخدمات:</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-clock text-primary me-2"></i>استقبال 24 ساعة</li>
                                    <li><i class="fas fa-luggage-cart text-primary me-2"></i>خدمة الأمتعة</li>
                                    <li><i class="fas fa-broom text-primary me-2"></i>تنظيف يومي</li>
                                    <li><i class="fas fa-shield-alt text-primary me-2"></i>أمان وحراسة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="hotels.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>العودة إلى قائمة الفنادق
                </a>
            </div>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/script.js"></script>
</body>
</html>
