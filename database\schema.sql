-- Drop existing tables if they exist (in reverse order of dependencies)
DROP TABLE IF EXISTS bookings;
DROP TABLE IF EXISTS trips;
DROP TABLE IF EXISTS trip_schedules;
DROP TABLE IF EXISTS gallery_images;
DROP TABLE IF EXISTS buses;
DROP TABLE IF EXISTS hotels;
DROP TABLE IF EXISTS cities;

-- Create cities table
CREATE TABLE cities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create hotels table
CREATE TABLE hotels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    city_id INT NOT NULL,
    stars INT NOT NULL CHECK (stars BETWEEN 1 AND 5),
    description TEXT,
    image_url VARCHAR(255),
    price_per_night DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (city_id) REFERENCES cities(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create buses table
CREATE TABLE buses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    capacity INT NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create trips table
CREATE TABLE trips (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    from_city_id INT NOT NULL,
    to_city_id INT NOT NULL,
    hotel_id INT NOT NULL,
    bus_id INT NOT NULL,
    start_date DATE NULL,
    end_date DATE NULL,
    description TEXT,
    image_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (from_city_id) REFERENCES cities(id),
    FOREIGN KEY (to_city_id) REFERENCES cities(id),
    FOREIGN KEY (hotel_id) REFERENCES hotels(id),
    FOREIGN KEY (bus_id) REFERENCES buses(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create bookings table
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trip_id INT NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    num_passengers INT NOT NULL,
    booking_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    FOREIGN KEY (trip_id) REFERENCES trips(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample data for cities
INSERT INTO cities (name) VALUES
('الرياض'),
('مكة المكرمة'),
('المدينة المنورة'),
('جدة'),
('الدمام');

-- Insert sample data for hotels
INSERT INTO hotels (name, city_id, stars, description, image_url, price_per_night) VALUES
('فندق مكة الكبير', 2, 5, 'فندق فاخر قريب من الحرم مع إطلالات رائعة', 'images/hotels/grand_makkah.jpg', 1200.00),
('هيلتون المدينة', 3, 4, 'إقامة مريحة قريبة من المسجد النبوي', 'images/hotels/madinah_hilton.jpg', 950.00),
('قصر الرياض', 1, 5, 'إقامة مميزة في قلب الرياض', 'images/hotels/riyadh_palace.jpg', 1100.00),
('منتجع جدة البحري', 4, 4, 'منتجع جميل مع إطلالات البحر الأحمر', 'images/hotels/jeddah_resort.jpg', 850.00),
('إطلالة الحرم', 2, 3, 'فندق اقتصادي مع إطلالة على الحرم', 'images/hotels/haram_view.jpg', 650.00),
('فندق الحرم الاقتصادي', 2, 3, 'فندق قريب من الحرم بأسعار مناسبة', 'images/hotels/budget_haram.jpg', 500.00),
('فندق المدينة الاقتصادي', 3, 3, 'فندق قريب من المسجد النبوي بأسعار مناسبة', 'images/hotels/budget_madinah.jpg', 450.00);

-- Insert sample data for buses
INSERT INTO buses (name, capacity, description, image_url) VALUES
('باص فاخر', 50, 'باص فاخر مكيف مع مقاعد قابلة للإمالة', 'images/buses/luxury_coach.jpg'),
('باص VIP', 25, 'خدمة باص مميزة مع مساحة إضافية للأرجل', 'images/buses/vip_shuttle.jpg'),
('باص عادي', 45, 'باص مريح للسفر الجماعي', 'images/buses/standard_bus.jpg'),
('نقل تنفيذي', 35, 'نقل درجة تنفيذية مع المرطبات', 'images/buses/executive_transport.jpg'),
('باص حديث', 40, 'باص حديث ومريح للرحلات الاقتصادية', 'images/buses/modern_bus.jpg');

-- Create trip schedules table
CREATE TABLE trip_schedules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trip_name VARCHAR(255) NOT NULL,
    trip_type VARCHAR(100) NOT NULL,
    duration VARCHAR(50) NOT NULL,
    day_of_week VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create gallery_images table
CREATE TABLE gallery_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_path VARCHAR(500) NOT NULL,
    category ENUM('buses', 'hotels') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample data for trips
INSERT INTO trips (title, from_city_id, to_city_id, hotel_id, bus_id, start_date, end_date, description, image_url) VALUES
('رحلة اقتصادية 3 أيام - مكة فقط', 1, 2, 6, 5, NULL, NULL, 'رحلة اقتصادية من الرياض إلى مكة المكرمة - فنادق قريبة من الحرم - باصات حديثة - دفع عند الوصول - متوفرة يومياً', 'images/trips/economy_makkah.jpg'),
('رحلة اقتصادية 3 أيام - مكة والمدينة', 1, 2, 6, 5, NULL, NULL, 'رحلة اقتصادية من الرياض إلى مكة والمدينة المنورة - فنادق قريبة من الحرمين - باصات حديثة - دفع عند الوصول - متوفرة يومياً', 'images/trips/economy_both.jpg'),
('رحلة VIP 3 أيام - مكة فقط', 1, 2, 1, 2, NULL, NULL, 'رحلة VIP من الرياض إلى مكة المكرمة - فنادق فاخرة قريبة من الحرم - دفع عند الوصول - الاثنين والخميس', 'images/trips/vip_makkah.jpg');

-- Insert trip schedules data
INSERT INTO trip_schedules (trip_name, trip_type, duration, day_of_week) VALUES
('رحلة يوم السبت', 'اقتصادية', '3 أيام', 'السبت'),
('رحلة عمرة يوم الأحد', 'اقتصادية', '3 أيام', 'الأحد'),
('رحلة عمرة اقتصادية - الاثنين', 'اقتصادية', '3 أيام', 'الاثنين'),
('رحلة عمرة VIP - الاثنين', 'VIP', '5 أيام', 'الاثنين'),
('رحلة عمرة يوم الثلاثاء', 'اقتصادية', '3 أيام', 'الثلاثاء'),
('حملة عمرة يوم الأربعاء', 'اقتصادية', '3 أيام', 'الأربعاء'),
('رحلة عمرة اقتصادية - الخميس', 'اقتصادية', '3 أيام', 'الخميس'),
('رحلة عمرة VIP - الخميس', 'VIP', '5 أيام', 'الخميس'),
('رحلة عمرة يوم الجمعة', 'اقتصادية', '3 أيام', 'الجمعة');