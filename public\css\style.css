/* Main Styles for Umrah Website */

/* General styles */
body {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
    line-height: 1.6;
    direction: rtl;
    text-align: right;
}

/* Arabic font support - optimized loading */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap');

/* Colors */
.bg-primary {
    background-color: #1e3a8a !important;
}

.text-primary {
    color: #1e3a8a !important;
}

.btn-primary {
    background-color: #1e3a8a;
    border-color: #1e3a8a;
}

.btn-primary:hover {
    background-color: #152a61;
    border-color: #152a61;
}

.btn-outline-primary {
    color: #1e3a8a;
    border-color: #1e3a8a;
}

.btn-outline-primary:hover {
    background-color: #1e3a8a;
    border-color: #1e3a8a;
}

/* Header and Navigation */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
}

/* Hero Section */
.hero {
    padding: 80px 0;
    background: linear-gradient(to right, #1e3a8a, #3b82f6);
}

/* Cards */
.card {
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-img-top {
    height: 180px;
    object-fit: cover;
}

/* Trip Card Enhanced Styles - Compact Version */
.trip-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border-radius: 10px;
    overflow: hidden;
    border: none;
    max-width: 100%;
}

.trip-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12) !important;
}

.trip-image {
    height: 160px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.trip-card:hover .trip-image {
    transform: scale(1.05);
}

.trip-placeholder {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.trip-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.placeholder-content {
    text-align: center;
    z-index: 1;
    position: relative;
    padding: 1rem;
}

.placeholder-text {
    color: white;
    font-size: 0.8rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    line-height: 1.2;
}

.trip-info-compact {
    font-size: 0.9rem;
}

.info-item-small {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.info-item-small i {
    width: 16px;
    text-align: center;
    font-size: 0.9rem;
}

.stars-small {
    display: inline-block;
    margin-right: 0.5rem;
}

.stars-small i {
    font-size: 0.7rem;
}

.trip-card .card-title {
    font-size: 1rem;
    line-height: 1.3;
    height: 2.6rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.trip-card .btn {
    border-radius: 6px;
    transition: all 0.2s ease-in-out;
    font-weight: 500;
    font-size: 0.85rem;
}

.trip-card .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.25);
}

.trip-card .card-footer {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Schedule Table Styles */
.schedule-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
}

.schedule-card .card-header {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    border: none;
    position: relative;
}

.schedule-card .card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #fbbf24, #f59e0b, #ef4444);
}

.schedule-table {
    font-size: 1rem;
}

.schedule-table thead th {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%) !important;
    border: none;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.schedule-row {
    transition: all 0.3s ease;
    border-bottom: 1px solid #e5e7eb;
}

.schedule-row:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.schedule-row:nth-child(even) {
    background-color: #f9fafb;
}

.schedule-row:nth-child(odd) {
    background-color: #ffffff;
}

.day-badge {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3);
    min-width: 80px;
    display: inline-block;
}

.trip-name {
    font-size: 1.1rem;
    color: #374151;
}

.badge-type {
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.economy-badge {
    background: linear-gradient(135deg, #10b981 0%, #**********%);
    color: white;
}

.vip-badge {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.info-box {
    padding: 1rem;
    border-radius: 10px;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    transition: transform 0.2s ease;
}

.info-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* About Company Section */
.about-image-container {
    position: relative;
    height: 100%;
    min-height: 400px;
}

.about-image-placeholder {
    width: 100%;
    height: 100%;
    min-height: 400px;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(30, 58, 138, 0.3);
}

.about-image-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.8) 0%, rgba(59, 130, 246, 0.7) 50%, rgba(30, 64, 175, 0.8) 100%);
    z-index: 1;
}

/* Default pattern if no image is set */
.about-image-placeholder:not([style*="background-image"])::before {
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.8) 0%, rgba(59, 130, 246, 0.7) 50%, rgba(30, 64, 175, 0.8) 100%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="islamic" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M10,2 L18,10 L10,18 L2,10 Z" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23islamic)"/></svg>');
}

.image-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 2;
    z-index: 2;
}

.image-overlay {
    margin-top: 2rem;
}

.section-badge {
    display: inline-block;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
}

.about-content {
    padding: 2rem 0;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    text-align: justify;
}

.features-list {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border-left: 4px solid #1e3a8a;
}

.feature-item {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: #374151;
    transition: color 0.2s ease;
}

.feature-item:hover {
    color: #1e3a8a;
}

.feature-item i {
    font-size: 1.1rem;
}

.cta-buttons .btn {
    border-radius: 10px;
    padding: 12px 30px;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

/* Gallery Section */
.gallery-tabs .nav-link {
    background: white;
    border: 2px solid #e5e7eb;
    color: #6b7280;
    border-radius: 25px;
    padding: 12px 30px;
    margin: 0 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.gallery-tabs .nav-link:hover {
    border-color: #1e3a8a;
    color: #1e3a8a;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.2);
}

.gallery-tabs .nav-link.active {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    border-color: #1e3a8a;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(30, 58, 138, 0.3);
}

.gallery-item {
    height: 280px;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.gallery-placeholder {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    overflow: hidden;
}

/* Bus Placeholders */
.bus-placeholder {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
}

.bus-placeholder-2 {
    background: linear-gradient(135deg, #059669 0%, #10b981 50%, #34d399 100%);
}

.bus-placeholder-3 {
    background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 50%, #a78bfa 100%);
}

/* Hotel Placeholders */
.hotel-placeholder {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 50%, #f87171 100%);
}

.hotel-placeholder-2 {
    background: linear-gradient(135deg, #d97706 0%, #f59e0b 50%, #fbbf24 100%);
}

.hotel-placeholder-3 {
    background: linear-gradient(135deg, #0891b2 0%, #06b6d4 50%, #22d3ee 100%);
}

.gallery-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="10" height="10" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.gallery-content {
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-content {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 3;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay .btn {
    border-radius: 20px;
    padding: 8px 20px;
    font-weight: 600;
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-overlay .btn {
    transform: translateY(0);
}

/* Modal Styles */
.modal-image-placeholder {
    height: 400px;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.modal-image-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="camera" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23camera)"/></svg>');
    opacity: 0.2;
}

.modal-image-content {
    position: relative;
    z-index: 2;
}

/* Real Images Styles */
.gallery-image-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 15px;
}

.gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-image {
    transform: scale(1.1);
}

.gallery-content-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.7) 100%);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-content-overlay {
    opacity: 1;
}

.gallery-content-overlay .gallery-content {
    align-self: flex-end;
    text-align: center;
}

.gallery-content-overlay .gallery-overlay {
    align-self: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Hotel Cards Styles */
.hotel-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.hotel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.hotel-image-placeholder {
    height: 200px;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.hotel-image-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hotel-image-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

/* Enhanced Hero Section */
.hero {
    min-height: 70vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    animation: heroFloat 20s ease-in-out infinite;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.85) 0%, rgba(59, 130, 246, 0.75) 100%);
    z-index: 1;
}

.hero .container {
    position: relative;
    z-index: 2;
}

@keyframes heroFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-10px) scale(1.02); }
}

.hero-buttons .btn {
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

/* Footer */
footer {
    background-color: #1a1a1a;
    color: #fff;
    padding: 40px 0 20px;
}

footer a {
    color: #ccc;
    text-decoration: none;
}

footer a:hover {
    color: #fff;
}

.footer-heading {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #fff;
}

.footer-links li {
    margin-bottom: 10px;
}

/* Admin panel */
.admin-sidebar {
    background-color: #f8f9fa;
    min-height: 100vh;
    padding-top: 20px;
}

.admin-content {
    padding: 20px;
}

.admin-nav .nav-link {
    color: #495057;
    border-radius: 0;
    padding: 12px 20px;
}

.admin-nav .nav-link:hover {
    background-color: #e9ecef;
}

.admin-nav .nav-link.active {
    background-color: #1e3a8a;
    color: #fff;
}

/* Detail pages */
.detail-image {
    max-height: 400px;
    object-fit: cover;
    border-radius: 8px;
}

/* Booking form */
.booking-form {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* RTL Support */
.navbar-nav {
    direction: rtl;
}

.navbar-brand {
    margin-left: 0;
    margin-right: auto;
}

/* Fixed WhatsApp Button */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.whatsapp-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #25D366;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.whatsapp-btn:hover {
    background: #128C7E;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    }
    50% {
        box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4), 0 0 0 10px rgba(37, 211, 102, 0.1);
    }
    100% {
        box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    }
}

/* Section Badge Styling */
.section-badge {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    padding: 12px 24px;
    color: white;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    min-width: 160px;
    text-align: center;
}

.section-badge:first-child {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.section-badge:last-child {
    background: linear-gradient(135deg, #10b981 0%, #**********%);
}

.section-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Mobile responsive for hero badges */
@media (max-width: 768px) {
    .hero-buttons {
        flex-direction: row !important;
        justify-content: center !important;
        gap: 10px !important;
    }

    .section-badge {
        min-width: 140px !important;
        font-size: 0.9rem !important;
        padding: 10px 16px !important;
    }
}

.ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

.me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

.text-md-start {
    text-align: right !important;
}

.text-md-end {
    text-align: left !important;
}

/* Performance optimizations */
.trip-card {
    contain: layout style paint;
}

.trip-card * {
    will-change: auto;
}

.trip-card:hover * {
    will-change: transform;
}

/* Reduce repaints */
.trip-card .info-item i {
    transform: translateZ(0);
}

.trip-card .btn {
    transform: translateZ(0);
}

/* Responsive */
@media (max-width: 767.98px) {
    .card-img-top {
        height: 150px;
    }

    .hero {
        padding: 50px 0;
    }

    .trip-card {
        margin-bottom: 1.5rem;
    }

    .trip-image {
        height: 140px;
    }

    .info-item-small {
        margin-bottom: 0.4rem;
    }

    .trip-card .card-title {
        font-size: 0.9rem;
        height: 2.4rem;
    }
}

@media (max-width: 575.98px) {
    .trip-card .card-body {
        padding: 0.75rem;
    }

    .trip-card .card-footer {
        padding: 0.5rem;
    }

    .trip-image {
        height: 120px;
    }

    .trip-info-compact {
        font-size: 0.8rem;
    }

    .trip-card .card-title {
        font-size: 0.85rem;
        height: 2.2rem;
    }
}

@media (min-width: 1200px) {
    .trip-image {
        height: 180px;
    }
}

/* About Section Responsive */
@media (max-width: 991.98px) {
    .about-content {
        padding: 1rem 0;
        text-align: center;
    }

    .about-image-placeholder {
        min-height: 300px;
    }

    .features-list {
        padding: 1.5rem;
        margin-top: 2rem;
    }
}

@media (max-width: 767.98px) {
    .about-image-placeholder {
        min-height: 250px;
    }

    .about-text p {
        font-size: 1rem;
        text-align: right;
    }

    .features-list {
        padding: 1rem;
    }

    .cta-buttons .btn {
        width: 100%;
        margin-bottom: 1rem;
    }

    /* Gallery Responsive */
    .gallery-tabs .nav-link {
        padding: 10px 20px;
        margin: 0 5px;
        font-size: 0.9rem;
    }

    .gallery-item {
        height: 220px;
        margin-bottom: 1rem;
    }

    .gallery-content h5 {
        font-size: 1rem;
    }

    .gallery-content p {
        font-size: 0.8rem;
    }

    .modal-image-placeholder {
        height: 300px;
    }
}

@media (max-width: 575.98px) {
    .gallery-tabs .nav-link {
        padding: 8px 15px;
        margin: 0 2px;
        font-size: 0.8rem;
    }

    .gallery-item {
        height: 200px;
    }

    .gallery-content i {
        font-size: 2rem !important;
    }

    .gallery-content h5 {
        font-size: 0.9rem;
    }

    .gallery-content p {
        font-size: 0.7rem;
    }

    .modal-image-placeholder {
        height: 250px;
    }

    .modal-image-content h4 {
        font-size: 1.2rem;
    }
}

/* Schedule Table Responsive */
@media (max-width: 767.98px) {
    .schedule-table {
        font-size: 0.85rem;
    }

    .day-badge {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-width: 70px;
    }

    .trip-name {
        font-size: 0.9rem;
    }

    .badge-type {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .schedule-table thead th {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }

    .schedule-row td {
        padding: 1rem 0.5rem;
    }

    .info-box {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }
}

@media (max-width: 575.98px) {
    .schedule-card .card-header h4 {
        font-size: 1.1rem;
    }

    .schedule-table {
        font-size: 0.8rem;
    }

    .day-badge {
        padding: 4px 8px;
        font-size: 0.7rem;
        min-width: 60px;
    }

    .trip-name {
        font-size: 0.8rem;
    }

    .badge-type {
        padding: 4px 8px;
        font-size: 0.7rem;
    }
}