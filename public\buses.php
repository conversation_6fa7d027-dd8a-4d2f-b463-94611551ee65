<?php
session_start();
require_once __DIR__ . '/../config/db_connect.php';
require_once __DIR__ . '/../config/functions.php';

// Get all buses
$buses = getBuses($pdo);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transportation - Umrah Services</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container py-5">
        <h1 class="mb-4 text-center">Our Transportation Fleet</h1>
        <p class="text-center lead mb-5">Comfortable and reliable transportation for your Umrah journey</p>

        <div class="row">
            <?php foreach ($buses as $bus): ?>
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="row g-0">
                        <div class="col-md-5">
                            <img src="<?= $bus['image_url'] ? $bus['image_url'] : 'images/buses/default.jpg' ?>" 
                                 class="img-fluid rounded-start h-100" style="object-fit: cover;" alt="<?= $bus['name'] ?>">
                        </div>
                        <div class="col-md-7">
                            <div class="card-body d-flex flex-column h-100">
                                <h5 class="card-title"><?= $bus['name'] ?></h5>
                                <p class="card-text">
                                    <i class="fas fa-users me-2"></i>Capacity: <?= $bus['capacity'] ?> passengers
                                </p>
                                <p class="card-text flex-grow-1">
                                    <?= $bus['description'] ?>
                                </p>
                                <div class="mt-auto">
                                    <a href="trips.php?bus=<?= $bus['id'] ?>" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i> Find Trips with this Transport
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/script.js"></script>
</body>
</html>