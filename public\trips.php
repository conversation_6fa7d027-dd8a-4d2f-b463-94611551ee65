<?php
session_start();
require_once __DIR__ . '/../config/db_connect.php';
require_once __DIR__ . '/../config/functions.php';

// Get all trips
$trips = getTrips($pdo);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جميع حملات العمرة من الرياض - الزائرين</title>
    <meta name="description" content="الزائرين - جميع حملات العمرة من الرياض مع أفضل الخدمات والأسعار المناسبة">
    <meta name="keywords" content="حملات عمرة من الرياض, الزائرين, عمرة, رحلات عمرة, الرياض">
    <link rel="canonical" href="https://alzaarin.net/trips.php">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container py-5">
        <h1 class="mb-4 text-center">جميع حملات العمرة من الرياض</h1>

        <!-- Trips List -->
        <div class="row">
            <?php if (empty($trips)): ?>
            <div class="col-12">
                <div class="alert alert-info">
                    لم يتم العثور على رحلات تطابق معاييرك. يرجى تجربة مرشحات مختلفة.
                </div>
            </div>
            <?php else: ?>
                <?php foreach ($trips as $trip): ?>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="row g-0">
                            <div class="col-md-5">
                                <?php if ($trip['image_url'] && file_exists($trip['image_url'])): ?>
                                    <img src="<?= $trip['image_url'] ?>" class="img-fluid rounded-start h-100" style="object-fit: cover;" alt="<?= $trip['title'] ?>">
                                <?php else: ?>
                                    <div class="trip-placeholder rounded-start h-100">
                                        <div class="placeholder-content">
                                            <i class="fas fa-kaaba fa-2x text-white mb-1"></i>
                                            <div class="placeholder-text small"><?= $trip['title'] ?></div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-7">
                                <div class="card-body">
                                    <h5 class="card-title"><?= $trip['title'] ?></h5>
                                    <p class="card-text">
                                        <i class="fas fa-map-marker-alt me-2"></i>من <?= $trip['from_city'] ?> إلى <?= $trip['to_city'] ?><br>
                                        <i class="fas fa-calendar me-2"></i>
                                        <?php if (strpos($trip['title'], 'VIP') !== false): ?>
                                            الاثنين والخميس
                                        <?php else: ?>
                                            متوفرة يومياً
                                        <?php endif; ?><br>
                                        <i class="fas fa-hotel me-2"></i><?= $trip['hotel_name'] ?>
                                        (<?php for($i = 0; $i < $trip['hotel_stars']; $i++): ?><i class="fas fa-star text-warning"></i><?php endfor; ?>)<br>
                                        <i class="fas fa-bus me-2"></i><?= $trip['bus_name'] ?><br>

                                    </p>
                                    <div class="mt-3">
                                        <a href="trip_details.php?id=<?= $trip['id'] ?>" class="btn btn-outline-primary">عرض التفاصيل</a>
                                        <a href="book_trip.php?id=<?= $trip['id'] ?>" class="btn btn-primary float-end">احجز الآن</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/script.js"></script>
</body>
</html>