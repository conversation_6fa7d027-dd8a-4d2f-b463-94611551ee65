<?php
session_start();
require_once __DIR__ . '/../config/db_connect.php';
require_once __DIR__ . '/../config/functions.php';

// Get WhatsApp number from settings
$whatsappNumber = getSetting($pdo, 'whatsapp_number', '966501234567');

$errors = [];
$success = false;

// Process contact form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $subject = sanitize($_POST['subject'] ?? '');
    $message = sanitize($_POST['message'] ?? '');
    
    // Check for errors
    if (empty($name)) {
        $errors['name'] = 'الاسم مطلوب';
    }

    if (empty($email)) {
        $errors['email'] = 'البريد الإلكتروني مطلوب';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = 'تنسيق البريد الإلكتروني غير صحيح';
    }

    if (empty($subject)) {
        $errors['subject'] = 'الموضوع مطلوب';
    }

    if (empty($message)) {
        $errors['message'] = 'الرسالة مطلوبة';
    }
    
    // If no errors, process the contact form
    if (empty($errors)) {
        // In a real application, you'd typically:
        // 1. Save to database
        // 2. Send email notification
        
        // For this demo, we'll just show success message
        $success = true;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اتصل بنا - رحلات العمرة من الرياض</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container py-5">
        <h1 class="mb-4 text-center">اتصل بنا</h1>
        <p class="text-center lead mb-5">نحن هنا للإجابة على أي أسئلة حول خدمات العمرة من الرياض</p>

        <div class="row">
            <div class="col-lg-5 mb-4 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h3 class="mb-4">تواصل معنا</h3>
                        <p>هل لديك أسئلة حول باقات العمرة أو تحتاج مساعدة في الحجز؟ تواصل مع فريقنا باستخدام المعلومات أدناه أو املأ نموذج الاتصال.</p>

                        <div class="d-flex mb-3">
                            <div class="me-3">
                                <i class="fas fa-map-marker-alt fa-2x text-primary"></i>
                            </div>
                            <div>
                                <h5>العنوان</h5>
                                <p class="mb-0">طريق الملك فهد 123، الرياض، المملكة العربية السعودية</p>
                            </div>
                        </div>
                        
                        <div class="d-flex mb-3">
                            <div class="me-3">
                                <i class="fab fa-whatsapp fa-2x text-success"></i>
                            </div>
                            <div>
                                <h5>واتساب</h5>
                                <p class="mb-0"><?= $whatsappNumber ?></p>
                                <a href="<?= getWhatsAppURL($pdo, 'مرحباً، أريد الاستفسار عن خدماتكم') ?>"
                                   target="_blank"
                                   class="btn btn-success btn-sm">
                                    <i class="fab fa-whatsapp me-2"></i>تواصل واتساب
                                </a>
                            </div>
                        </div>
                        
                        <div class="d-flex mb-3">
                            <div class="me-3">
                                <i class="fas fa-envelope fa-2x text-primary"></i>
                            </div>
                            <div>
                                <h5>Email</h5>
                                <p class="mb-0"><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="d-flex mb-3">
                            <div class="me-3">
                                <i class="fas fa-clock fa-2x text-primary"></i>
                            </div>
                            <div>
                                <h5>Business Hours</h5>
                                <p class="mb-0">Sunday - Thursday: 9:00 AM - 6:00 PM<br>
                                Friday - Saturday: 10:00 AM - 2:00 PM</p>
                            </div>
                        </div>
                        
                        <h5 class="mt-4 mb-3">Follow Us</h5>
                        <div class="d-flex">
                            <a href="#" class="me-3 text-primary fs-4">
                                <i class="fab fa-facebook"></i>
                            </a>
                            <a href="#" class="me-3 text-primary fs-4">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="me-3 text-primary fs-4">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="text-primary fs-4">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-7">
                <div class="card">
                    <div class="card-body">
                        <h3 class="mb-4">Send Us a Message</h3>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <h4 class="alert-heading">Thank you for your message!</h4>
                                <p>We have received your inquiry and will respond to you as soon as possible.</p>
                            </div>
                        <?php else: ?>
                            <form method="post" class="needs-validation" novalidate>
                                <div class="mb-3">
                                    <label for="name" class="form-label">Your Name *</label>
                                    <input type="text" class="form-control <?= isset($errors['name']) ? 'is-invalid' : '' ?>" 
                                           id="name" name="name" required 
                                           value="<?= htmlspecialchars($_POST['name'] ?? '') ?>">
                                    <?php if (isset($errors['name'])): ?>
                                        <div class="invalid-feedback"><?= $errors['name'] ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Your Email *</label>
                                    <input type="email" class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                           id="email" name="email" required 
                                           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                                    <?php if (isset($errors['email'])): ?>
                                        <div class="invalid-feedback"><?= $errors['email'] ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Subject *</label>
                                    <input type="text" class="form-control <?= isset($errors['subject']) ? 'is-invalid' : '' ?>" 
                                           id="subject" name="subject" required 
                                           value="<?= htmlspecialchars($_POST['subject'] ?? '') ?>">
                                    <?php if (isset($errors['subject'])): ?>
                                        <div class="invalid-feedback"><?= $errors['subject'] ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">Message *</label>
                                    <textarea class="form-control <?= isset($errors['message']) ? 'is-invalid' : '' ?>" 
                                              id="message" name="message" rows="5" required><?= htmlspecialchars($_POST['message'] ?? '') ?></textarea>
                                    <?php if (isset($errors['message'])): ?>
                                        <div class="invalid-feedback"><?= $errors['message'] ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <a href="<?= getWhatsAppURL($pdo, 'مرحباً، أريد الاستفسار عن خدماتكم') ?>"
                                   target="_blank"
                                   class="btn btn-success w-100">
                                    <i class="fab fa-whatsapp me-2"></i>تواصل واتساب
                                </a>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/script.js"></script>
</body>
</html>