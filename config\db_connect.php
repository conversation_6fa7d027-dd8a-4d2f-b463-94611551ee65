<?php
// Database Configuration
$host = "localhost";
$dbname = "umrah_db55";
$username = "root";
$password = "";

// Create a database connection
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    // Set the PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    // Set default fetch mode to associative array
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    // Use UTF-8 encoding
    $pdo->exec("SET NAMES 'utf8'");
} catch(PDOException $e) {
    die("ERROR: Could not connect. " . $e->getMessage());
}
?>